import sqlite3

def check_deep_roles():
    conn = sqlite3.connect('data/sqlite/change_requests.db')
    cursor = conn.cursor()
    
    print("🔍 DEEP ROLES ANALYSIS")
    print("=" * 80)
    
    # Check all users and their roles
    print("\n📋 ALL USERS AND THEIR ROLES:")
    print("-" * 60)
    
    users = cursor.execute('''
        SELECT u.id, u.username, u.role as primary_role, u.department,
               GROUP_CONCAT(ur.role_name, ', ') as additional_roles
        FROM users u
        LEFT JOIN user_roles ur ON u.id = ur.user_id
        GROUP BY u.id, u.username, u.role, u.department
        ORDER BY u.username
    ''').fetchall()
    
    for user in users:
        user_id, username, primary_role, department, additional_roles = user
        print(f"👤 {username} (ID: {user_id})")
        print(f"   Primary Role: {primary_role}")
        print(f"   Department: {department}")
        print(f"   Additional Roles: {additional_roles or 'None'}")
        print()
    
    # Specifically check BSU users for team leader roles
    print("\n🎯 BSU USERS WITH TEAM LEADER ROLES:")
    print("-" * 60)
    
    bsu_users = cursor.execute('''
        SELECT u.id, u.username, u.role as primary_role, u.department,
               GROUP_CONCAT(ur.role_name, ', ') as additional_roles
        FROM users u
        LEFT JOIN user_roles ur ON u.id = ur.user_id
        WHERE u.role = 'bsu_head' OR ur.role_name LIKE '%lead%'
        GROUP BY u.id, u.username, u.role, u.department
        ORDER BY u.username
    ''').fetchall()
    
    for user in bsu_users:
        user_id, username, primary_role, department, additional_roles = user
        print(f"👤 {username} (ID: {user_id})")
        print(f"   Primary Role: {primary_role}")
        print(f"   Department: {department}")
        print(f"   Additional Roles: {additional_roles or 'None'}")
        print()
    
    # Check all team leader roles
    print("\n👥 ALL TEAM LEADER ROLES:")
    print("-" * 60)
    
    team_leads = cursor.execute('''
        SELECT DISTINCT ur.role_name, COUNT(ur.user_id) as user_count
        FROM user_roles ur
        WHERE ur.role_name LIKE '%lead%'
        GROUP BY ur.role_name
        ORDER BY ur.role_name
    ''').fetchall()
    
    for role, count in team_leads:
        print(f"🎯 {role}: {count} users")
    
    # Check specific users mentioned in testing
    print("\n🔍 SPECIFIC USERS FOR TESTING:")
    print("-" * 60)
    
    test_users = ['Lukongodo', 'Kangoni', 'Namboya', 'Kulubya', 'Kibas', 'Kamchira']
    for username in test_users:
        user_info = cursor.execute('''
            SELECT u.id, u.username, u.role as primary_role, u.department,
                   GROUP_CONCAT(ur.role_name, ', ') as additional_roles
            FROM users u
            LEFT JOIN user_roles ur ON u.id = ur.user_id
            WHERE u.username = ?
            GROUP BY u.id, u.username, u.role, u.department
        ''', (username,)).fetchone()
        
        if user_info:
            user_id, username, primary_role, department, additional_roles = user_info
            print(f"👤 {username} (ID: {user_id})")
            print(f"   Primary Role: {primary_role}")
            print(f"   Department: {department}")
            print(f"   Additional Roles: {additional_roles or 'None'}")
            print()
    
    conn.close()

if __name__ == '__main__':
    check_deep_roles()



















