/**
 * Change Request Notification Engine
 * Generates role-based email notifications for queue actions
 */

class NotificationEngine {
    constructor() {
        this.roleConfigurations = {
            department_head: {
                actionMessage: "A new change request from your department requires your approval before it can proceed to the next stage.",
                buttonText: "Review & Approve Request",
                subject: "Approval Required",
                urgentSubject: "🔴 URGENT: Approval Required"
            },
            bsu_head: {
                actionMessage: "A change request has been approved and is now in your queue for business analyst assignment and initial review.",
                buttonText: "Assign Business Analyst",
                subject: "Assignment Needed",
                urgentSubject: "🔴 URGENT: Assignment Needed"
            },
            business_analyst: {
                actionMessage: "You have been assigned as the business analyst for this change request. Please begin your analysis and documentation.",
                buttonText: "Start Analysis",
                subject: "Analysis Required",
                urgentSubject: "🔴 URGENT: Analysis Required"
            },
            it_head: {
                actionMessage: "A change request has completed business analysis and requires your technical review and approval for implementation.",
                buttonText: "Technical Review",
                subject: "Technical Review",
                urgentSubject: "🔴 URGENT: Technical Review"
            },
            transport_officer: {
                actionMessage: "A change request has been approved for implementation and is ready for transport execution.",
                buttonText: "Execute Transport",
                subject: "Implementation Ready",
                urgentSubject: "🔴 URGENT: Implementation Ready"
            },
            // New configurations for user notifications
            requester_submission: {
                actionMessage: "Your change request has been submitted successfully and is awaiting approval from your department head.",
                buttonText: "View Request",
                subject: "Request Submitted Successfully",
                urgentSubject: "✅ Request Submitted Successfully"
            },
            requester_ba_assignment: {
                actionMessage: "A business analyst has been assigned to your change request and will begin analysis shortly.",
                buttonText: "View Request",
                subject: "Business Analyst Assigned",
                urgentSubject: "👨‍💼 Business Analyst Assigned"
            },
            admin: {
                actionMessage: "A change request requires administrative attention or has been escalated.",
                buttonText: "Review Request",
                subject: "Administrative Action Required",
                urgentSubject: "🔴 URGENT: Administrative Action Required"
            },
            tester: {
                actionMessage: "A change request is ready for testing and requires your validation.",
                buttonText: "Begin Testing",
                subject: "Testing Required",
                urgentSubject: "🔴 URGENT: Testing Required"
            }
        };

        this.emailTemplate = this.getEmailTemplate();
    }

    /**
     * Generate notification email for a change request
     * @param {Object} request - The change request object
     * @param {Object} recipient - The recipient user object
     * @param {string} actionType - Type of action needed
     * @returns {Object} Email object with subject, html, and text
     */
    generateNotificationEmail(request, recipient, actionType = 'queue_action') {
        const roleConfig = this.getRoleConfiguration(recipient.role);
        const variables = this.buildTemplateVariables(request, recipient, roleConfig);
        
        return {
            to: recipient.email,
            subject: this.generateSubject(request, roleConfig),
            html: this.replaceTemplateVariables(this.emailTemplate, variables),
            text: this.generateTextVersion(variables)
        };
    }

    /**
     * Generate multiple notifications for role-based routing
     * @param {Object} request - The change request object
     * @param {Array} recipients - Array of recipient objects with roles
     * @returns {Array} Array of email objects
     */
    generateBulkNotifications(request, recipients) {
        return recipients.map(recipient => 
            this.generateNotificationEmail(request, recipient)
        );
    }

    /**
     * Generate notification email with magic link for direct action
     * @param {Object} request - The change request object
     * @param {Object} recipient - The recipient user object
     * @param {string} actionType - Type of action needed
     * @param {string} magicLink - Magic link URL for direct action
     * @returns {Object} Email object with subject, html, and text
     */
    generateNotificationEmailWithMagicLink(request, recipient, actionType = 'queue_action', magicLink = null) {
        const roleConfig = this.getRoleConfiguration(recipient.role);
        const variables = this.buildTemplateVariables(request, recipient, roleConfig);
        
        // Add magic link to variables
        if (magicLink) {
            variables.magic_link = magicLink;
            variables.action_button_text = "Take Action Now (No Login Required)";
        }
        
        return {
            to: recipient.email,
            subject: this.generateSubject(request, roleConfig),
            html: this.replaceTemplateVariables(this.emailTemplate, variables),
            text: this.generateTextVersion(variables),
            magic_link: magicLink
        };
    }

    /**
     * Generate magic link URL for a specific action
     * @param {Object} request - The change request object
     * @param {Object} recipient - The recipient user object
     * @param {string} actionPath - The action path (e.g., "/requests/123")
     * @returns {string} Magic link URL
     */
    generateMagicLinkUrl(request, recipient, actionPath) {
        const baseUrl = this.getSystemUrl();
        const magicLinkToken = this.generateMagicLinkToken(recipient.id, request.id);
        return `${baseUrl}/api/auth/magic-link/${magicLinkToken}?redirect=${encodeURIComponent(actionPath)}`;
    }

    /**
     * Generate magic link token (placeholder - should be implemented in backend)
     * @param {number} userId - User ID
     * @param {number} requestId - Request ID
     * @returns {string} Magic link token
     */
    generateMagicLinkToken(userId, requestId) {
        // This should be implemented in the backend
        // For now, return a placeholder
        return `magic_${userId}_${requestId}_${Date.now()}`;
    }

    /**
     * Get role configuration
     * @param {string} role - User role
     * @returns {Object} Role configuration
     */
    getRoleConfiguration(role) {
        return this.roleConfigurations[role] || {
            actionMessage: "A change request requires your attention.",
            buttonText: "View Request",
            subject: "Action Required",
            urgentSubject: "🔴 URGENT: Action Required"
        };
    }

    /**
     * Get notification configuration for a user
     * @param {Object} user - User object with notification preferences
     * @returns {Object} Notification configuration
     */
    getNotificationConfig(user) {
        const defaultConfig = {
            email_enabled: true,
            urgent_only: false,
            daily_digest: false,
            weekly_summary: false,
            escalation_notifications: true
        };
        
        return {
            ...defaultConfig,
            ...(user.notification_preferences || {})
        };
    }

    /**
     * Check if notification should be sent based on user preferences and request priority
     * @param {Object} user - User object
     * @param {Object} request - Change request object
     * @param {string} notificationType - Type of notification
     * @returns {boolean} Whether notification should be sent
     */
    shouldSendNotification(user, request, notificationType = 'standard') {
        const config = this.getNotificationConfig(user);
        
        // Check if email is disabled
        if (!config.email_enabled) {
            return false;
        }
        
        // Check urgent-only preference
        if (config.urgent_only && request.priority !== 'high') {
            return false;
        }
        
        // Check escalation notifications
        if (notificationType === 'escalation' && !config.escalation_notifications) {
            return false;
        }
        
        return true;
    }

    /**
     * Build template variables from request and recipient data
     * @param {Object} request - Change request
     * @param {Object} recipient - Recipient user
     * @param {Object} roleConfig - Role configuration
     * @returns {Object} Template variables
     */
    buildTemplateVariables(request, recipient, roleConfig) {
        return {
            // Recipient info
            recipient_name: recipient.full_name || recipient.username,
            recipient_role: recipient.role,
            action_message: roleConfig.actionMessage,
            action_button_text: roleConfig.buttonText,
            
            // Request details - use display_id (formatted ID) instead of internal sequence number
            request_id: request.display_id || request.id || request.request_id,
            request_title: request.title,
            description: this.truncateText(request.description, 200),
            priority: this.formatPriority(request.priority),
            priority_color: this.getPriorityColor(request.priority),
            change_type: this.formatChangeType(request.change_type || request.changeType),
            environment: request.environment,
            urgency_level: request.urgency_level || request.urgencyLevel,
            
            // People & dates
            requester_name: request.requester?.full_name || request.requester_name || 'System User',
            requester_department: request.requester?.department || request.department || 'Unknown',
            submission_date: this.formatDate(request.created_at || request.submission_date),
            expected_completion: this.calculateExpectedCompletion(request),
            
            // URLs
            request_url: `${this.getSystemUrl()}/request/${request.id || request.request_id}`,
            system_url: this.getSystemUrl()
        };
    }

    /**
     * Generate email subject based on priority
     * @param {Object} request - Change request
     * @param {Object} roleConfig - Role configuration
     * @returns {string} Email subject
     */
    generateSubject(request, roleConfig) {
        // Use display_id (formatted ID like SIM-0001) instead of internal sequence number
        const requestId = request.display_id || request.id || request.request_id;
        const title = request.title;
        const priority = request.priority;

        const baseSubject = `${roleConfig.subject}: Change Request ${requestId} - ${title}`;

        return priority === 'high'
            ? `${roleConfig.urgentSubject}: Change Request ${requestId}`
            : baseSubject;
    }

    /**
     * Get priority color for styling
     * @param {string} priority - Priority level
     * @returns {string} CSS color value
     */
    getPriorityColor(priority) {
        const colors = {
            high: '#ef4444',
            medium: '#f59e0b',
            low: '#10b981'
        };
        return colors[priority] || colors.medium;
    }

    /**
     * Format change type for display
     * @param {string} type - Change type
     * @returns {string} Formatted type
     */
    formatChangeType(type) {
        const types = {
            feature: 'New Feature',
            bugfix: 'Bug Fix',
            enhancement: 'Enhancement',
            security: 'Security Update',
            configuration: 'Configuration Change'
        };
        return types[type] || type || 'General Change';
    }

    /**
     * Format priority for display
     * @param {string} priority - Priority level
     * @returns {string} Formatted priority
     */
    formatPriority(priority) {
        return priority ? priority.charAt(0).toUpperCase() + priority.slice(1) : 'Medium';
    }

    /**
     * Calculate expected completion date
     * @param {Object} request - Change request
     * @returns {string} Formatted completion date
     */
    calculateExpectedCompletion(request) {
        const urgencyDays = {
            immediate: 1,
            urgent: 3,
            normal: 14,
            low: 30
        };
        
        const urgency = request.urgency_level || request.urgencyLevel || 'normal';
        const days = urgencyDays[urgency] || 14;
        
        const completionDate = new Date();
        completionDate.setDate(completionDate.getDate() + days);
        
        return this.formatDate(completionDate);
    }

    /**
     * Format date for display
     * @param {string|Date} date - Date to format
     * @returns {string} Formatted date
     */
    formatDate(date) {
        if (!date) return new Date().toLocaleDateString();
        
        const dateObj = date instanceof Date ? date : new Date(date);
        return dateObj.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    }

    /**
     * Truncate text to specified length
     * @param {string} text - Text to truncate
     * @param {number} maxLength - Maximum length
     * @returns {string} Truncated text
     */
    truncateText(text, maxLength) {
        if (!text || text.length <= maxLength) return text || '';
        return text.substring(0, maxLength) + '...';
    }

    /**
     * Generate digest email for multiple requests
     * @param {Object} user - User object
     * @param {Array} requests - Array of change request objects
     * @param {string} digestType - Type of digest (daily, weekly)
     * @returns {Object} Email object with subject, html, and text
     */
    generateDigestEmail(user, requests, digestType = 'daily') {
        const requestCount = requests.length;
        const subject = `${digestType.charAt(0).toUpperCase() + digestType.slice(1)} Digest: ${requestCount} items need your attention`;
        
        const requestsList = requests.map(request => {
            const priority = this.formatPriority(request.priority);
            const priorityColor = this.getPriorityColor(request.priority);
            return `
                <div style="background: #f8fafc; border: 1px solid #e2e8f0; border-radius: 6px; padding: 15px; margin: 10px 0;">
                    <h4 style="margin: 0 0 8px 0; color: #1f2937;">Request ${request.display_id || request.id}: ${request.title}</h4>
                    <p style="margin: 4px 0; color: #6b7280;">
                        <span style="background: ${priorityColor}; color: white; padding: 2px 8px; border-radius: 4px; font-size: 12px; font-weight: 600;">${priority}</span>
                        <span style="margin-left: 10px;">${this.formatChangeType(request.change_type)}</span>
                    </p>
                    <p style="margin: 8px 0 0 0; color: #374151;">${this.truncateText(request.description, 150)}</p>
                </div>
            `;
        }).join('');

        const html = `
            <div style="font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; max-width: 600px; margin: 0 auto; background: white; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px 20px; text-align: center;">
                    <h1 style="margin: 0;">📊 ${digestType.charAt(0).toUpperCase() + digestType.slice(1)} Digest</h1>
                    <p style="margin: 10px 0 0 0;">ChangeMasterPro Summary</p>
                </div>
                
                <div style="padding: 30px 20px;">
                    <h2 style="color: #1f2937; margin: 0 0 20px 0;">Hello ${user.full_name || user.username},</h2>
                    
                    <div style="background: #eff6ff; border-radius: 6px; padding: 20px; margin: 20px 0;">
                        <p style="margin: 0; font-size: 18px; color: #1e40af;">
                            You have <strong>${requestCount}</strong> change request${requestCount !== 1 ? 's' : ''} that need${requestCount === 1 ? 's' : ''} your attention.
                        </p>
                    </div>
                    
                    ${requestsList}
                    
                    <div style="text-align: center; margin: 30px 0;">
                        <a href="${this.getSystemUrl()}" style="display: inline-block; background: #3b82f6; color: white; padding: 12px 24px; border-radius: 6px; text-decoration: none; font-weight: 600;">
                            View All Requests
                        </a>
                    </div>
                </div>
                
                <div style="background: #f8fafc; padding: 20px; text-align: center; font-size: 14px; color: #6b7280;">
                    <p style="margin: 0;">This is an automated digest from ChangeMasterPro</p>
                    <p style="margin: 5px 0 0 0;">© 2024 ChangeMasterPro. All rights reserved.</p>
                </div>
            </div>
        `;

        const text = `
${digestType.charAt(0).toUpperCase() + digestType.slice(1)} Digest - ChangeMasterPro

Hello ${user.full_name || user.username},

You have ${requestCount} change request${requestCount !== 1 ? 's' : ''} that need${requestCount === 1 ? 's' : ''} your attention:

${requests.map(request =>
    `• Request ${request.display_id || request.id}: ${request.title} (${this.formatPriority(request.priority)} priority)`
).join('\n')}

Access the system at: ${this.getSystemUrl()}

---
This is an automated digest from ChangeMasterPro
© 2024 ChangeMasterPro. All rights reserved.
        `.trim();

        return {
            to: user.email,
            subject: subject,
            html: html,
            text: text
        };
    }

    /**
     * Get system URL
     * @returns {string} System base URL
     */
    getSystemUrl() {
        if (typeof window !== 'undefined') {
            return `${window.location.protocol}//${window.location.host}`;
        }
        return 'http://localhost:4950'; // Fallback for server-side
    }

    /**
     * Replace template variables in content
     * @param {string} template - Template string
     * @param {Object} variables - Variables to replace
     * @returns {string} Processed template
     */
    replaceTemplateVariables(template, variables) {
        let result = template;
        
        Object.entries(variables).forEach(([key, value]) => {
            const regex = new RegExp(`{${key}}`, 'g');
            result = result.replace(regex, value || '');
        });
        
        return result;
    }

    /**
     * Generate plain text version of email
     * @param {Object} variables - Template variables
     * @returns {string} Plain text email content
     */
    generateTextVersion(variables) {
        return `
ACTION REQUIRED - ChangeMasterPro

Hello ${variables.recipient_name},

${variables.action_message}

REQUEST DETAILS:
- Request ID: ${variables.request_id}
- Title: ${variables.request_title}
- Requester: ${variables.requester_name} (${variables.requester_department})
- Priority: ${variables.priority}
- Type: ${variables.change_type}
- Environment: ${variables.environment}
- Submitted: ${variables.submission_date}
- Expected Completion: ${variables.expected_completion}

Description:
${variables.description}

To take action on this request, please visit:
${variables.request_url}

---
This is an automated notification from ChangeMasterPro.
Please do not reply to this email.
        `.trim();
    }

    /**
     * Get the HTML email template
     * @returns {string} HTML template
     */
    getEmailTemplate() {
        return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Change Request - Action Required</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5; }
        .container { max-width: 600px; margin: 0 auto; background: white; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px 20px; text-align: center; }
        .content { padding: 30px 20px; }
        .request-card { background: #f8fafc; border: 1px solid #e2e8f0; border-radius: 8px; padding: 20px; margin: 20px 0; }
        .priority-high { border-left: 4px solid #ef4444; }
        .priority-medium { border-left: 4px solid #f59e0b; }
        .priority-low { border-left: 4px solid #10b981; }
        .action-button { display: inline-block; background: #3b82f6; color: white; padding: 12px 24px; border-radius: 6px; text-decoration: none; font-weight: 600; margin: 20px 0; }
        .footer { background: #f8fafc; padding: 20px; text-align: center; font-size: 14px; color: #6b7280; }
        .role-specific { padding: 15px; background: #eff6ff; border-radius: 6px; margin: 15px 0; }
        .details-table { width: 100%; border-collapse: collapse; }
        .details-table td { padding: 8px 0; vertical-align: top; }
        .details-table .label { font-weight: 600; width: 30%; }
        .timeline-notice { background: #fef3c7; border: 1px solid #f59e0b; border-radius: 6px; padding: 15px; margin: 20px 0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔔 Action Required</h1>
                            <p>ChangeMasterPro Notification</p>
        </div>
        
        <div class="content">
            <h2>Hello {recipient_name},</h2>
            
            <div class="role-specific">
                <strong>{action_message}</strong>
            </div>
            
            <div class="request-card priority-{priority}">
                <h3>📋 Request Details</h3>
                <table class="details-table">
                    <tr>
                        <td class="label">Request ID:</td>
                        <td>{request_id}</td>
                    </tr>
                    <tr>
                        <td class="label">Title:</td>
                        <td>{request_title}</td>
                    </tr>
                    <tr>
                        <td class="label">Requester:</td>
                        <td>{requester_name} ({requester_department})</td>
                    </tr>
                    <tr>
                        <td class="label">Priority:</td>
                        <td><span style="text-transform: uppercase; font-weight: 600; color: {priority_color};">{priority}</span></td>
                    </tr>
                    <tr>
                        <td class="label">Type:</td>
                        <td>{change_type}</td>
                    </tr>
                    <tr>
                        <td class="label">Environment:</td>
                        <td>{environment}</td>
                    </tr>
                    <tr>
                        <td class="label">Submitted:</td>
                        <td>{submission_date}</td>
                    </tr>
                </table>
                
                <div style="margin-top: 15px;">
                    <strong>Description:</strong>
                    <p style="margin: 8px 0; padding: 10px; background: white; border-radius: 4px; border: 1px solid #e5e7eb;">{description}</p>
                </div>
            </div>
            
            <div style="text-align: center;">
                <a href="{request_url}" class="action-button">
                    {action_button_text}
                </a>
            </div>
            
            <div class="timeline-notice">
                <strong>⏰ Timeline:</strong>
                <p style="margin: 5px 0 0 0;">Expected completion: {expected_completion}</p>
            </div>
        </div>
        
        <div class="footer">
                            <p>This is an automated notification from ChangeMasterPro</p>
            <p>Please do not reply to this email. For support, contact the system administrator.</p>
                            <p>&copy; 2024 ChangeMasterPro. All rights reserved.</p>
        </div>
    </div>
</body>
</html>
        `.trim();
    }
}

// Usage examples for integration
const notificationEngine = new NotificationEngine();

/**
 * Example: Send notification when request needs department head approval
 */
function notifyDepartmentHeadApproval(changeRequest, departmentHead) {
    const email = notificationEngine.generateNotificationEmail(
        changeRequest,
        departmentHead,
        'approval_required'
    );
    
    console.log('Sending approval notification:', email);
    return email;
}

/**
 * Example: Send notification when BSU head needs to assign BA
 */
function notifyBSUHeadAssignment(changeRequest, bsuHead) {
    const email = notificationEngine.generateNotificationEmail(
        changeRequest,
        bsuHead,
        'assignment_needed'
    );
    
    console.log('Sending assignment notification:', email);
    return email;
}

/**
 * Example: Send notifications to multiple recipients
 */
function notifyMultipleRoles(changeRequest, recipients) {
    const emails = notificationEngine.generateBulkNotifications(
        changeRequest,
        recipients
    );
    
    console.log(`Generated ${emails.length} notifications:`, emails);
    return emails;
}

/**
 * Test function to verify notification engine functionality
 * @returns {Object} Test results
 */
function testNotificationEngine() {
    console.log('🧪 Testing Notification Engine...');
    
    try {
        // Test data
        const testRequest = {
            id: 'TEST-001',
            title: 'Test Change Request',
            description: 'This is a test change request to verify notification functionality.',
            priority: 'high',
            change_type: 'feature',
            environment: 'production',
            urgency_level: 'urgent',
            requester: {
                full_name: 'Test User',
                department: 'IT'
            },
            created_at: new Date().toISOString()
        };
        
        const testUser = {
            id: 1,
            full_name: 'John Doe',
            email: '<EMAIL>',
            role: 'department_head',
            notification_preferences: {
                email_enabled: true,
                urgent_only: false
            }
        };
        
        // Test 1: Generate basic notification
        const notification = notificationEngine.generateNotificationEmail(testRequest, testUser);
        console.log('✅ Basic notification generated:', notification.subject);
        
        // Test 2: Test role configuration
        const roleConfig = notificationEngine.getRoleConfiguration('department_head');
        console.log('✅ Role configuration retrieved:', roleConfig.subject);
        
        // Test 3: Test notification preferences
        const shouldSend = notificationEngine.shouldSendNotification(testUser, testRequest);
        console.log('✅ Notification preference check:', shouldSend);
        
        // Test 4: Test digest email
        const digestEmail = notificationEngine.generateDigestEmail(testUser, [testRequest], 'daily');
        console.log('✅ Digest email generated:', digestEmail.subject);
        
        // Test 5: Test magic link generation
        const magicLink = notificationEngine.generateMagicLinkUrl(testRequest, testUser, '/requests/TEST-001');
        console.log('✅ Magic link generated:', magicLink.includes('magic_'));
        
        console.log('🎉 All notification engine tests passed!');
        
        return {
            success: true,
            tests_passed: 5,
            message: 'Notification engine is working correctly'
        };
        
    } catch (error) {
        console.error('❌ Notification engine test failed:', error);
        return {
            success: false,
            error: error.message,
            message: 'Notification engine test failed'
        };
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { NotificationEngine, notificationEngine, testNotificationEngine };
}

// Global browser compatibility
if (typeof window !== 'undefined') {
    window.NotificationEngine = NotificationEngine;
    window.notificationEngine = notificationEngine;
    window.testNotificationEngine = testNotificationEngine;
} 